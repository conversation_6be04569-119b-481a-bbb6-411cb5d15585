import re
import urllib.parse as urlparse
import logging
import logging
from bs4 import BeautifulSoup, NavigableString, Tag

logger = logging.getLogger(__name__)

def normalize_date(date_str):
    """
    Standardize the date string, supporting various common formats, returning yyyy/mm/dd format.
    """
    # 预处理：合并多行、去除多余空白和特殊字符
    date_str = str(date_str).replace('\n', ' ').replace('\r', ' ')
    date_str = re.sub(r'[\u3000\t]+', ' ', date_str)  # 全角空格、制表符
    date_str = re.sub(r'\s+', ' ', date_str).strip()
    if not date_str:
        return ""
    # 优先匹配 YYYY年MM月DD日（可带时间）
    m = re.search(r'(\d{4})[年\-/\.]?(\d{1,2})[月/\-.]?(\d{1,2})日?', date_str)
    if m:
        y, mth, d = m.groups()
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    # 匹配2位年份，自动补20xx
    m = re.search(r'(\d{2})[年\-/\.]?(\d{1,2})[月/\-.]?(\d{1,2})日?', date_str)
    if m:
        y, mth, d = m.groups()
        y = f"20{y}" if int(y) < 50 else f"19{y}"
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    # 匹配YYYYMMDD或YYYY-MM-DD等
    m = re.search(r'(\d{4})[\-/\.]?(\d{1,2})[\-/\.]?(\d{1,2})', date_str)
    if m:
        y, mth, d = m.groups()
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    m = re.search(r'(\d{2})[\-/\.]?(\d{1,2})[\-/\.]?(\d{1,2})', date_str)
    if m:
        y, mth, d = m.groups()
        y = f"20{y}" if int(y) < 50 else f"19{y}"
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    # 8位数字如20250625
    m = re.search(r'(\d{4})(\d{2})(\d{2})', date_str)
    if m:
        y, mth, d = m.groups()
        return f"{y}/{mth}/{d}"
    m = re.search(r'(\d{2})(\d{2})(\d{2})', date_str)
    if m:
        y, mth, d = m.groups()
        y = f"20{y}" if int(y) < 50 else f"19{y}"
        return f"{y}/{mth}/{d}"
    # 若未匹配到任何格式，直接返回原始字符串
    # 只保留年月日数字
    m = re.search(r'(\d{4})[年\-/\.]?(\d{1,2})[月/\-.]?(\d{1,2})', date_str)
    if m:
        y, mth, d = m.groups()
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    m = re.search(r'(\d{2})[年\-/\.]?(\d{1,2})[月/\-.]?(\d{1,2})', date_str)
    if m:
        y, mth, d = m.groups()
        y = f"20{y}" if int(y) < 50 else f"19{y}"
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    return ""

def get_source_prefixes():
    """
    获取来源前缀列表

    Returns:
        list: 来源前缀列表
    """
    return [
        "来源：", "来源:",
        "信息来源：", "信息来源:",
        "来源单位：", "来源单位:",
        "文章来源：", "文章来源:"
    ]

def normalize_source(src_str):
    """
    Standardize the source string:
    1. Remove common source prefixes
    2. Truncate content after the first space (usually the date)
    3. Handle special quote situations
    4. New: Filter out everything before and including the equals sign
    """
    src_str = src_str.strip()

    # 定义需要去除的来源前缀列表
    source_prefixes = get_source_prefixes()

    # 逐个检查并去除前缀
    for prefix in source_prefixes:
        if src_str.startswith(prefix):
            src_str = src_str[len(prefix):].lstrip()
            break

    # 特殊处理：去除开头可能的中文引号
    if src_str.startswith("“") and "”" in src_str:
        # 提取引号内的内容（保留引号内的空格）
        start_index = 1
        end_index = src_str.find("”", 1)
        if end_index != -1:
            src_str = src_str[start_index:end_index] + src_str[end_index+1:]

    # 截断第一个空格后的所有内容
    if " " in src_str:
        src_str = src_str.split(" ", 1)[0]

    # 新增：移除等于号及之前的内容（例如docSource="经济委"）
    import re
    src_str = re.sub(r'[^=]*=[^=]*=', '', src_str)

    return src_str

def extract_js_doc_source(text):
    """
    从JavaScript代码中提取docSource变量的值，支持多种JavaScript模式

    Args:
        text (str): 包含JavaScript代码的文本

    Returns:
        str: 提取到的docSource值, 如果没有找到则返回空字符串
    """
    import re

    if not text:
        return ""

    # 1. 首先尝试提取docSource变量值
    if 'docSource' in text:
        # 匹配 var docSource="值"; 或 var docSource='值';
        # 支持多行和空格变化
        patterns = [
            r'var\s+docSource\s*=\s*"([^"]*)"',  # 双引号
            r'var\s+docSource\s*=\s*\'([^\']*)\'',  # 单引号
            r'docSource\s*=\s*"([^"]*)"',  # 不带var的双引号
            r'docSource\s*=\s*\'([^\']*)\'',  # 不带var的单引号
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                source_value = match.group(1).strip()
                if source_value:  # 确保不是空字符串
                    return source_value

    # 2. 如果docSource为空或未找到，尝试提取document.write()中的内容
    if 'document.write' in text:
        # 匹配document.write("内容")或document.write('内容')
        write_patterns = [
            r'document\.write\s*\(\s*"([^"]+)"\s*\)',  # 双引号
            r'document\.write\s*\(\s*\'([^\']+)\'\s*\)',  # 单引号
        ]

        for pattern in write_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                # 清理提取的内容，去除HTML标签和多余空格
                cleaned = re.sub(r'<[^>]*>', '', match).strip()
                if cleaned and len(cleaned) > 1:
                    return cleaned

    # 3. 尝试从条件语句中提取备用来源
    if 'if' in text and 'else' in text:
        # 匹配类似 if (docSource.length == 0) { document.write("备用来源"); }
        conditional_patterns = [
            r'if\s*\([^)]*docSource[^)]*\)\s*\{[^}]*document\.write\s*\(\s*"([^"]+)"\s*\)',
            r'if\s*\([^)]*docSource[^)]*\)\s*\{[^}]*document\.write\s*\(\s*\'([^\']+)\'\s*\)',
        ]

        for pattern in conditional_patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                cleaned = re.sub(r'<[^>]*>', '', match.group(1)).strip()
                if cleaned and len(cleaned) > 1:
                    return cleaned

    return ""

def clean_source_text(source_text):
    """
    健壮的来源文本清理函数，去除时间信息、HTML标签、特殊字符等无关内容

    Args:
        source_text (str): 原始来源文本

    Returns:
        str: 清理后的来源文本
    """
    if not source_text:
        return ""

    import re

    # 转换为字符串并去除首尾空白
    text = str(source_text).strip()

    # 1. 去除HTML标签
    text = re.sub(r'<[^>]*>', '', text)

    # 2. 去除JavaScript代码片段
    text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.DOTALL | re.IGNORECASE)

    # 3. 去除常见的时间格式
    time_patterns = [
        r'\s*时间[：:]\s*[^\s]*',  # 时间：2022-05-27
        r'\s*\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?\s*\d{1,2}:\d{2}(:\d{2})?',  # 完整时间戳
        r'\s*\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?',  # 日期
        r'\s*\d{4}/\d{1,2}/\d{1,2}[^\s]*',  # 2022/05/27
        r'\s*\d{1,2}:\d{2}(:\d{2})?',  # 时间部分
    ]

    for pattern in time_patterns:
        text = re.sub(pattern, '', text)

    # 4. 去除作者信息
    author_patterns = [
        r'\s*作者[：:]\s*[^\s]*',
        r'\s*编辑[：:]\s*[^\s]*',
        r'\s*责任编辑[：:]\s*[^\s]*',
        r'\s*记者[：:]\s*[^\s]*',
    ]

    for pattern in author_patterns:
        text = re.sub(pattern, '', text)

    # 5. 去除多余的标点符号和空白字符
    text = re.sub(r'[，,。；;！!？?]+$', '', text)  # 去除末尾标点
    text = re.sub(r'^\s*[，,。；;！!？?]+', '', text)  # 去除开头标点
    text = re.sub(r'\s+', ' ', text)  # 合并多个空格
    text = re.sub(r'[\u3000]+', ' ', text)  # 全角空格转半角

    # 6. 去除引号（如果整个内容被引号包围）
    text = text.strip()
    if (text.startswith('"') and text.endswith('"')) or \
       (text.startswith("'") and text.endswith("'")) or \
       (text.startswith(""") and text.endswith(""")) or \
       (text.startswith("'") and text.endswith("'")):
        text = text[1:-1].strip()

    # 7. 去除括号（如果整个内容被括号包围）
    if (text.startswith('(') and text.endswith(')')) or \
       (text.startswith('（') and text.endswith('）')):
        text = text[1:-1].strip()

    return text

def extract_source_from_text(text):
    """
    健壮的来源信息提取函数，支持多种提取策略

    提取策略优先级：
    1. JavaScript代码中的来源信息
    2. 标准前缀匹配
    3. 智能文本分析
    4. 正则表达式模式匹配

    Args:
        text (str): 包含来源信息的文本

    Returns:
        str: 提取到的来源信息, 如果没有找到则返回清理后的原文本
    """
    if not text:
        return ""

    original_text = text.strip()

    # 策略1: 首先尝试从JavaScript中提取来源信息
    js_source = extract_js_doc_source(original_text)
    if js_source:
        return clean_source_text(js_source)

    # 策略2: 使用传统的前缀匹配
    source_prefixes = get_source_prefixes()
    for prefix in source_prefixes:
        if prefix in original_text:
            # 找到前缀后，提取前缀后面的内容
            source = original_text.split(prefix)[-1].strip()
            source = clean_source_text(source)
            if source and len(source) > 1:
                return source

    # 策略3: 智能文本分析 - 寻找可能的来源模式
    source = _extract_source_by_patterns(original_text)
    if source:
        return source

    # 策略4: 如果所有策略都失败，返回清理后的原文本
    cleaned_text = clean_source_text(original_text)
    return cleaned_text if cleaned_text else original_text


def _extract_source_by_patterns(text):
    """
    使用正则表达式模式匹配来源信息

    Args:
        text (str): 输入文本

    Returns:
        str: 提取到的来源信息，如果没有找到则返回空字符串
    """
    import re

    # 常见的来源模式
    patterns = [
        # 匹配：政协珠海市委员会     时间：2021-07-15
        r'([^\s]+(?:委员会|政府|政协|人大|党委|办公室|局|厅|部|院|中心|网站|新闻|日报|晚报|电视台|广播|微信|公众号))[^a-zA-Z]*?(?:时间：|\d{4}[-/年])',

        # 匹配：来源后面跟着机构名称
        r'(?:来源[：:]?\s*)([^\s]+(?:委员会|政府|政协|人大|党委|办公室|局|厅|部|院|中心|网站|新闻|日报|晚报|电视台|广播|微信|公众号))',

        # 匹配：引号内的来源信息
        r'["""]([^"""]+(?:委员会|政府|政协|人大|党委|办公室|局|厅|部|院|中心|网站|新闻|日报|晚报|电视台|广播|微信|公众号))[^"""]*["""]',

        # 匹配：括号内的来源信息
        r'[（(]([^）)]+(?:委员会|政府|政协|人大|党委|办公室|局|厅|部|院|中心|网站|新闻|日报|晚报|电视台|广播|微信|公众号))[^）)]*[）)]',

        # 匹配：冒号后的机构名称
        r'[:：]\s*([^\s]+(?:委员会|政府|政协|人大|党委|办公室|局|厅|部|院|中心|网站|新闻|日报|晚报|电视台|广播|微信|公众号))',
    ]

    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            cleaned_match = clean_source_text(match)
            if cleaned_match and len(cleaned_match) > 2:
                return cleaned_match

    return ""
    
   
def clean_html_content(html):
    """
    清洗HTML正文，去除无关标签，保留结构化文本，并尽量还原表格结构。
    """
    soup = BeautifulSoup(html, 'html.parser')
    # 移除不需要的标签
    for element in soup(['script', 'style', 'meta', 'link', 'o:p']):
        element.decompose()
    # 处理Office文档命名空间声明
    for attr in ['xmlns:o', 'xmlns:v', 'xmlns:w']:
        if soup.html and hasattr(soup.html, 'attrs') and attr in soup.html.attrs:
            del soup.html[attr]
    # 处理特殊换行标签
    for br in soup.find_all('br'):
        br.replace_with('\n')

    def process_node(node):
        if isinstance(node, NavigableString):
            text = node.strip().replace('\xa0', ' ').replace('\u3000', ' ').replace('\u200b', '')
            return text
        if isinstance(node, Tag):
            # 表格行处理
            if node.name == 'tr':
                cells = []
                for cell in node.find_all(['td', 'th'], recursive=False):
                    cells.append(process_node(cell))
                return '\t'.join(cells) + '\n'
            # 表格单元格处理
            if node.name in ['td', 'th']:
                return ''.join(process_node(child) for child in node.contents)
            # 忽略 span/font/o:p 只递归内容
            if node.name in ['span', 'font', 'o:p', 'div']:
                return ''.join(process_node(child) for child in node.contents)
            # p标签加换行
            if node.name == 'p':
                content = ''.join(process_node(child) for child in node.contents)
                return content.strip() + '\n'
            return ''.join(process_node(child) for child in node.contents)
        return ''

    text = process_node(soup)
    # 后处理：清理多余空白和多余换行
    text = re.sub(r'[ \t\u3000]+', ' ', text)
    text = re.sub(r' *\n *', '\n', text)
    text = re.sub(r'\n{3,}', '\n\n', text)
    text = re.sub(r' +\n', '\n', text)
    return text.strip()


# 全局黑名单正则（可扩展）
FILTER_BLACKLIST = [
    r'ZJEG_RSS\.content\.begin',
    r'ZJEG_RSS\.content\.end',
    # 新增：过滤<$[信息内容]>begin到<$[信息内容]>end及其后内容
    r'<\$\[信息内容\]>begin[\s\S]*?(<\$\[信息内容\]>end[\s\S]*)?',
    # 新增：过滤<$[信息内容]>end及其后所有内容
    r'<\$\[信息内容\]>end[\s\S]*$',
    # 新增分享语句过滤（匹配包含中文冒号和空格的情况）
    r'相关附件：[\s\S]*?\$\(\'#share-1\'\)\.share\(\);',
    # 新增：过滤JavaScript附件下载代码
    r'//附件下载的显示过滤[\s\S]*?\$\(\'#share-1\'\)\.share;',
    r'var\s+s\s*=\s*[\'"][\'"];\s*if\s*\(\s*s\s*==\s*[\'"][\'"][\s\S]*?document\.write\([\'"]附件下载:[\'"][\s\S]*?\$\(\'#share-1\'\)\.share;'
]

def filter_content(text, patterns=None):
    """增强型内容过滤"""
    if text is None:  # 处理空内容
        return ""
    
    # 合并全局黑名单和自定义过滤规则
    all_patterns = FILTER_BLACKLIST.copy()
    if patterns:
        if not isinstance(patterns, list):
            patterns = [patterns]
        all_patterns.extend(patterns)

    # 先处理特殊内容块：<$[信息内容]>begin ... <$[信息内容]>end 及其后内容
    text = re.sub(r'<\$\[信息内容\]>begin[\s\S]*?(<\$\[信息内容\]>end[\s\S]*)?', '', text)

    # 其他黑名单正则
    for pattern in all_patterns:
        if pattern == r'<\$\[信息内容\]>begin[\s\S]*?(<\$\[信息内容\]>end[\s\S]*)?':
            continue  # 已处理
        try:
            text = re.sub(pattern, '', text)
        except (TypeError, re.error) as e:
            print(f"过滤规则异常: {pattern} ({str(e)})")
            continue
    return text.strip()

def clean_title(title):
    """
    标准化标题，去除首尾空白、特殊符号、换行等。
    """
    if not isinstance(title, str):
        return ''
    title = title.strip()
    # 可根据需要扩展更多清洗规则
    title = title.replace('\n', ' ').replace('\r', ' ')
    title = re.sub(r'[\s\u3000]+', ' ', title)  # 合并多余空白
    title = re.sub(r'[\u200b\ufeff]', '', title)  # 去除零宽字符

    # 新增：去除“来源：xxx 作者：xxx 日期”尾部信息
    # 典型格式：xxx 来源：xxx 作者：xxx yyyy/m/d hh:mm:ss
    # 1. 先去除“来源：...”及后续内容
    title = re.sub(r'[\s\u3000]*来源：.*?(作者：.*)?\d{4}[年/-]\d{1,2}[月/-]\d{1,2}[日]? ?\d{1,2}:\d{2}(:\d{2})?', '', title)
    # 2. 再去除“作者：...”及后续内容
    title = re.sub(r'[\s\u3000]*作者：.*?\d{4}[年/-]\d{1,2}[月/-]\d{1,2}[日]? ?\d{1,2}:\d{2}(:\d{2})?', '', title)
    # 3. 再去除“来源：...”及后续内容（无作者）
    title = re.sub(r'[\s\u3000]*来源：.*', '', title)
    # 4. 再去除“作者：...”及后续内容（无来源）
    title = re.sub(r'[\s\u3000]*作者：.*', '', title)
    # 5. 去除末尾日期（如有）
    title = re.sub(r'[\s\u3000]*\d{4}[年/-]\d{1,2}[月/-]\d{1,2}[日]? ?\d{1,2}:\d{2}(:\d{2})?', '', title)
    # 6. 去除末尾多余空格和标点
    title = re.sub(r'[，,。\s]+$', '', title)
    return title



def clean_text(text):
    """清理文本内容"""
    if not text:
        return ""
    
    # 移除HTML标签
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(str(text), 'html.parser')
    text = soup.get_text()
    
    # 标准化空白字符
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    
    return text


def normalize_url_for_deduplication(url):
    """
    规范化URL用于去重比较（简化版本，避免过度处理）

    Args:
        url: 原始URL

    Returns:
        规范化后的URL
    """
    if not url:
        return url

    # 只进行最基本的规范化，避免过度处理导致URL损坏
    try:
        # 去除片段标识符 (#xxx)
        if '#' in url:
            url = url.split('#')[0]

        # 去除末尾的空白字符
        url = url.strip()

        return url

    except Exception as e:
        # 如果处理失败，返回原始URL
        logger.debug(f"URL规范化失败，使用原始URL: {url}, 错误: {e}")
        return url


# ==================== 扩展的文本清洗功能 ====================

def clean_non_text_fields(data):
    """
    清洗非文本字段，包括数字、特殊字符、HTML标签等

    Args:
        data: 可以是字符串、数字、列表或字典

    Returns:
        清洗后的数据
    """
    if data is None:
        return ""

    # 如果是字符串，进行清洗
    if isinstance(data, str):
        return clean_string_field(data)

    # 如果是数字，转换为字符串并清洗
    elif isinstance(data, (int, float)):
        return clean_numeric_field(data)

    # 如果是列表，递归清洗每个元素
    elif isinstance(data, list):
        return [clean_non_text_fields(item) for item in data]

    # 如果是字典，递归清洗每个值
    elif isinstance(data, dict):
        return {key: clean_non_text_fields(value) for key, value in data.items()}

    # 其他类型转换为字符串并清洗
    else:
        return clean_string_field(str(data))


def clean_string_field(text):
    """
    Clean non-text content from the string field.

    Args:
        text: 输入字符串

    Returns:
        清洗后的字符串
        Cleaned string
    """
    if not isinstance(text, str):
        text = str(text)

    # 1. 去除HTML标签和实体
    text = clean_html_tags(text)

    # 2. 去除特殊字符和控制字符
    text = clean_special_characters(text)

    # 3. 规范化空白字符
    text = normalize_whitespace(text)

    # 4. 去除多余的标点符号
    text = clean_punctuation(text)

    # 5. 去除数字噪音（如页码、编号等）
    text = clean_numeric_noise(text)

    return text.strip()


def clean_html_tags(text):
    """去除HTML标签和实体，保留换行结构"""
    # 先将换行相关的HTML标签转换为换行符
    # 处理段落标签
    text = re.sub(r'</p>\s*<p[^>]*>', '\n\n', text, flags=re.IGNORECASE)
    text = re.sub(r'</p>', '\n', text, flags=re.IGNORECASE)
    text = re.sub(r'<p[^>]*>', '', text, flags=re.IGNORECASE)

    # 处理换行标签
    text = re.sub(r'<br\s*/?>', '\n', text, flags=re.IGNORECASE)

    # 处理标题标签（添加换行）
    text = re.sub(r'</h[1-6]>', '\n', text, flags=re.IGNORECASE)
    text = re.sub(r'<h[1-6][^>]*>', '', text, flags=re.IGNORECASE)

    # 处理div标签（可能包含段落）
    text = re.sub(r'</div>\s*<div[^>]*>', '\n', text, flags=re.IGNORECASE)
    text = re.sub(r'</?div[^>]*>', '\n', text, flags=re.IGNORECASE)

    # 去除其他HTML标签
    text = re.sub(r'<[^>]+>', '', text)

    # 去除HTML实体
    html_entities = {
        '&nbsp;': ' ',
        '&lt;': '<',
        '&gt;': '>',
        '&amp;': '&',
        '&quot;': '"',
        '&apos;': "'",
        '&copy;': '©',
        '&reg;': '®',
        '&trade;': '™',
        '&hellip;': '…',
        '&mdash;': '—',
        '&ndash;': '–',
        '&ldquo;': '"',
        '&rdquo;': '"',
        '&lsquo;': ''',
        '&rsquo;': '''
    }

    for entity, replacement in html_entities.items():
        text = text.replace(entity, replacement)

    # 去除其他数字HTML实体 &#xxx;
    text = re.sub(r'&#\d+;', '', text)

    # 去除其他命名HTML实体 &xxx;
    text = re.sub(r'&[a-zA-Z]+;', '', text)

    return text


def clean_special_characters(text):
    """去除特殊字符和控制字符"""
    # 去除零宽字符
    text = re.sub(r'[\u200b\u200c\u200d\ufeff]', '', text)

    # 去除其他不可见字符
    text = re.sub(r'[\u0000-\u001f\u007f-\u009f]', '', text)

    # 去除特殊Unicode字符（保留基本标点）
    text = re.sub(r'[^\u0020-\u007e\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]', '', text)

    # 去除多余的特殊符号
    text = re.sub(r'[▪▫■□●○◆◇★☆※→←↑↓]', '', text)

    return text


def normalize_whitespace(text):
    """规范化空白字符（保留换行符）"""
    # 将全角空格转换为半角空格
    text = text.replace('\u3000', ' ')

    # 合并多个空格和制表符，但保留换行符
    text = re.sub(r'[ \t]+', ' ', text)

    # 去除行首行尾空白，但保留换行符
    text = re.sub(r'^[ \t]+|[ \t]+$', '', text, flags=re.MULTILINE)

    # 清理换行符周围的多余空格
    text = re.sub(r' *\n *', '\n', text)

    # 限制连续换行符的数量（最多保留2个连续换行）
    text = re.sub(r'\n{3,}', '\n\n', text)

    return text


def clean_punctuation(text):
    """清理多余的标点符号"""
    # 去除重复的标点符号
    text = re.sub(r'([。！？，、；：])\1+', r'\1', text)

    # 去除行首的标点符号
    text = re.sub(r'^[，、；：。！？]+', '', text, flags=re.MULTILINE)

    # 去除多余的括号
    text = re.sub(r'\(\s*\)', '', text)
    text = re.sub(r'\[\s*\]', '', text)
    text = re.sub(r'【\s*】', '', text)

    return text


def clean_numeric_noise(text):
    """去除数字噪音"""
    # 去除单独的数字（可能是页码、编号等）
    text = re.sub(r'^\d+$', '', text, flags=re.MULTILINE)

    # 去除行首的数字编号
    text = re.sub(r'^\d+[\.、]\s*', '', text, flags=re.MULTILINE)

    # 去除时间戳格式的数字
    text = re.sub(r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', '', text)

    # 去除电话号码格式
    text = re.sub(r'\d{3,4}-\d{7,8}', '', text)
    text = re.sub(r'1[3-9]\d{9}', '', text)

    return text


def clean_numeric_field(number):
    """
    清洗数字字段

    Args:
        number: 输入数字

    Returns:
        清洗后的字符串
    """
    if number is None:
        return ""

    # 转换为字符串
    text = str(number)

    # 去除科学计数法
    if 'e' in text.lower():
        try:
            # 尝试转换为普通数字格式
            if isinstance(number, float):
                text = f"{number:.10f}".rstrip('0').rstrip('.')
            else:
                text = str(int(float(text)))
        except:
            pass

    # 去除多余的小数点和零
    if '.' in text:
        text = text.rstrip('0').rstrip('.')

    return text


# ==================== 增强型内容过滤功能 ====================

def enhanced_content_filter(text, patterns=None):
    """
    增强型正文过滤函数
    结合传统过滤和非文本字段清洗功能，保留换行符

    Args:
        text: 输入文本
        patterns: 自定义过滤规则

    Returns:
        清洗后的文本（保留换行符）
    """
    if text is None:
        return ""

    # 第一步：HTML标签清洗（转换为换行符）
    text = clean_html_tags(text)

    # 第二步：传统内容过滤（跳过HTML清洗，因为已经处理过了）
    text = filter_content_skip_html(text, patterns)

    # 第三步：非文本字段清洗（保留换行符）
    text = clean_string_field_preserve_newlines(text)

    # 第四步：简单的无用内容清洗
    text = clean_useless_content_simple(text)

    return text


def filter_content_skip_html(text, patterns=None):
    """
    传统内容过滤，但跳过HTML清洗（因为已经处理过了）

    Args:
        text: 输入文本
        patterns: 自定义过滤规则

    Returns:
        过滤后的文本
    """
    if patterns:
        # 如果有自定义规则，应用它们
        for pattern in patterns:
            text = re.sub(pattern, '', text)

    return text


def clean_string_field_preserve_newlines(text):
    """
    保留换行符的字符串字段清洗

    Args:
        text: 输入字符串

    Returns:
        清洗后的字符串（保留换行符）
    """
    if not isinstance(text, str):
        text = str(text)

    # 1. 去除特殊字符和控制字符（但保留换行符）
    text = clean_special_characters_preserve_newlines(text)

    # 2. 规范化空白字符（保留换行符）
    text = normalize_whitespace(text)

    # 3. 轻度清理标点符号
    text = clean_punctuation_conservative(text)

    return text.strip()


def clean_special_characters_preserve_newlines(text):
    """保留换行符的特殊字符清洗"""
    # 只去除零宽字符和明显的噪音字符，保留换行符
    text = re.sub(r'[\u200b\u200c\u200d\ufeff]', '', text)

    # 去除其他不可见字符，但保留换行符(\n)和回车符(\r)
    text = re.sub(r'[\u0000-\u0008\u000b\u000c\u000e-\u001f\u007f-\u009f]', '', text)

    # 只去除明显的装饰性符号，保留常用符号
    text = re.sub(r'[▪▫■□●○◆◇★☆※]', '', text)

    return text


def clean_useless_content_simple(text):
    """
    简单的无用内容清洗

    Args:
        text: 输入文本

    Returns:
        清洗后的文本
    """
    if not text:
        return text

    # 按行处理，去除明显无用的行
    lines = text.split('\n')
    cleaned_lines = []

    for line in lines:
        line = line.strip()

        # 保留空行（用于段落分隔）
        if not line:
            cleaned_lines.append('')
            continue

        # 检查是否为明显无用的行
        if is_useless_line_simple(line):
            continue

        cleaned_lines.append(line)

    # 重新组合，限制连续空行
    result = '\n'.join(cleaned_lines)
    result = re.sub(r'\n{3,}', '\n\n', result)

    return result.strip()


def is_useless_line_simple(line):
    """
    简单判断是否为无用行

    Args:
        line: 输入行

    Returns:
        bool: 是否为无用行
    """
    if not line or len(line.strip()) < 3:
        return True

    # 简单的无用行模式
    useless_patterns = [
        r'^发布时间：',
        r'^更新时间：',
        r'^编辑：',
        r'^责任编辑：',
        r'^作者：',
        r'^来源：',
        r'^版权所有',
        r'^Copyright',
        r'^©',
        r'^分享到：',
        r'^点击.*分享',
        r'^评论\s*\(\s*\d+\s*\)',
        r'^点赞\s*\(\s*\d+\s*\)',
        r'^阅读\s*\(\s*\d+\s*\)',
        r'^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}',
    ]

    for pattern in useless_patterns:
        if re.match(pattern, line, re.IGNORECASE):
            return True

    return False


# ==================== 保守清洗功能 ====================

def clean_string_field_conservative(text):
    """
    保守的字符串字段清洗，保留更多有用内容

    Args:
        text: 输入字符串

    Returns:
        清洗后的字符串
    """
    if not isinstance(text, str):
        text = str(text)

    # 1. 去除HTML标签和实体（但保留文本内容）
    text = clean_html_tags(text)

    # 2. 去除特殊字符和控制字符（但保留基本标点）
    text = clean_special_characters_conservative(text)

    # 3. 规范化空白字符
    text = normalize_whitespace(text)

    # 4. 轻度清理标点符号（不要过度清理）
    text = clean_punctuation_conservative(text)

    return text.strip()


def clean_special_characters_conservative(text):
    """保守的特殊字符清洗"""
    # 只去除零宽字符和明显的噪音字符
    text = re.sub(r'[\u200b\u200c\u200d\ufeff]', '', text)

    # 去除其他不可见字符
    text = re.sub(r'[\u0000-\u001f\u007f-\u009f]', '', text)

    # 只去除明显的装饰性符号，保留常用符号
    text = re.sub(r'[▪▫■□●○◆◇★☆※]', '', text)

    return text


def clean_punctuation_conservative(text):
    """保守的标点符号清理"""
    # 只去除重复的标点符号
    text = re.sub(r'([。！？，、；：])\1+', r'\1', text)

    # 去除空括号
    text = re.sub(r'\(\s*\)', '', text)
    text = re.sub(r'\[\s*\]', '', text)
    text = re.sub(r'【\s*】', '', text)

    return text


# ==================== 智能内容清洗功能 ====================

def clean_content_specific(text):
    """
    针对正文内容的特定清洗规则（智能模式）

    Args:
        text: 输入文本

    Returns:
        清洗后的文本
    """
    if not text:
        return text

    # 分割文本为句子，进行智能过滤
    sentences = re.split(r'[。！？\n]', text)
    filtered_sentences = []

    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue

        # 检查是否为有用的正文内容
        if is_useful_content(sentence):
            # 对有用内容进行轻度清洗
            cleaned_sentence = light_clean_sentence(sentence)
            if cleaned_sentence:
                filtered_sentences.append(cleaned_sentence)

    # 重新组合文本
    result = '。'.join(filtered_sentences)
    if result and not result.endswith('。'):
        result += '。'

    # 最终清理
    result = re.sub(r'\s+', ' ', result)  # 合并多余空格
    result = result.strip()

    return result


def is_useful_content(sentence):
    """
    判断句子是否为有用的正文内容

    Args:
        sentence: 输入句子

    Returns:
        bool: 是否为有用内容
    """
    if not sentence or len(sentence.strip()) < 5:
        return False

    # 无用内容的特征
    useless_patterns = [
        r'^首页\s*[>›]',  # 导航
        r'^当前位置：',
        r'^您现在的位置：',
        r'^位置：',
        r'^导航：',
        r'^网站导航',
        r'^版权所有',
        r'^Copyright',
        r'^©.*保留',
        r'^联系我们',
        r'^免责声明',
        r'^隐私政策',
        r'^分享到：',
        r'^分享：',
        r'^扫一扫',
        r'^关注.*微信',
        r'^点击.*分享',
        r'^转发.*朋友圈',
        r'^评论\s*\(',
        r'^点赞\s*\(',
        r'^阅读\s*\(',
        r'^浏览\s*\(',
        r'^发表评论',
        r'^我要评论',
        r'^发布时间：',
        r'^更新时间：',
        r'^最后更新：',
        r'^编辑：',
        r'^责任编辑：',
        r'^审核：',
        r'^校对：',
        r'^记者：.*报道$',
        r'^作者：',
        r'^相关链接：',
        r'^相关阅读：',
        r'^推荐阅读：',
        r'^延伸阅读：',
        r'^更多.*：',
        r'^标签：',
        r'^alert\s*\(',
        r'^function\s+',
        r'^var\s+',
        r'^document\.',
        r'^window\.',
    ]

    for pattern in useless_patterns:
        if re.match(pattern, sentence, re.IGNORECASE):
            return False

    # 检查是否只包含数字、符号或HTML标签
    text_only = re.sub(r'[^\w\u4e00-\u9fff]', '', sentence)
    if len(text_only) < 3:
        return False

    # 检查是否为纯数字或日期
    if re.match(r'^\d{4}[-年]\d{1,2}[-月]\d{1,2}', sentence):
        return False

    return True


def light_clean_sentence(sentence):
    """
    对有用句子进行轻度清洗

    Args:
        sentence: 输入句子

    Returns:
        清洗后的句子
    """
    # 去除JavaScript代码片段
    sentence = re.sub(r'alert\s*\([^)]*\)', '', sentence)
    sentence = re.sub(r'function\s+\w+\s*\([^)]*\)\s*\{[^}]*\}', '', sentence)

    # 去除残留的HTML词汇（但要小心不要误删正文中的这些词）
    if len(sentence) < 20:  # 只对短句进行HTML词汇清理
        sentence = re.sub(r'\b(HTML|CSS|JavaScript|script|div|span)\b', '', sentence, flags=re.IGNORECASE)

    # 清理多余空格
    sentence = re.sub(r'\s+', ' ', sentence)
    sentence = sentence.strip()

    return sentence


# ==================== 平衡模式清洗功能 ====================

def clean_content_balanced(text):
    """
    平衡模式的正文清洗：既清除无用内容，又保留有用内容和换行符

    Args:
        text: 输入文本

    Returns:
        清洗后的文本（保留换行符）
    """
    if not text:
        return text

    # 使用行级别的清洗，保留换行符结构
    lines = text.split('\n')
    filtered_lines = []

    for line in lines:
        stripped_line = line.strip()

        # 保留空行（但限制连续空行数量）
        if not stripped_line:
            # 如果前一行不是空行，则保留这个空行
            if filtered_lines and filtered_lines[-1].strip():
                filtered_lines.append('')
            continue

        # 检查是否为明显的无用行
        if is_obviously_useless_line(stripped_line):
            continue

        # 对保留的行进行轻度清洗
        cleaned_line = clean_line_lightly(stripped_line)
        if cleaned_line and len(cleaned_line.strip()) > 2:
            filtered_lines.append(cleaned_line)

    # 重新组合文本，保留换行符
    result = '\n'.join(filtered_lines)

    # 最终清理：只合并过多的连续空行，但保留基本的换行结构
    result = re.sub(r'\n\s*\n\s*\n\s*\n+', '\n\n\n', result)  # 最多保留3个连续换行
    result = re.sub(r'[ \t]+', ' ', result)  # 只合并空格和制表符，不影响换行
    result = result.strip()

    return result


def is_obviously_useless_line(line):
    """
    判断是否为明显无用的行

    Args:
        line: 输入行

    Returns:
        bool: 是否为明显无用
    """
    if not line or len(line.strip()) < 3:
        return True

    # 明显无用的行模式（更严格的匹配）
    useless_patterns = [
        r'^首页\s*[>›]\s*.*[>›]\s*正文\s*$',  # 完整的面包屑导航
        r'^当前位置：.*$',
        r'^您现在的位置：.*$',
        r'^版权所有.*$',
        r'^Copyright.*$',
        r'^©.*$',
        r'^联系我们.*$',
        r'^免责声明.*$',
        r'^隐私政策.*$',
        r'^分享到：.*$',
        r'^扫一扫.*二维码.*$',
        r'^关注.*微信.*$',
        r'^发布时间：\d{4}-\d{2}-\d{2}.*$',
        r'^更新时间：\d{4}-\d{2}-\d{2}.*$',
        r'^编辑：.*$',
        r'^责任编辑：.*$',
        r'^作者：.*$',
        r'^评论\s*\(\s*\d+\s*\)\s*$',
        r'^点赞\s*\(\s*\d+\s*\)\s*$',
        r'^阅读\s*\(\s*\d+\s*\)\s*$',
        r'^相关阅读：.*$',
        r'^推荐阅读：.*$',
        r'^标签：.*$',
        r'^\d{4}年\d{1,2}月\d{1,2}日\s*$',  # 纯日期行
        r'^alert\s*\(.*\)\s*;?\s*$',  # JavaScript alert
    ]

    for pattern in useless_patterns:
        if re.match(pattern, line, re.IGNORECASE):
            return True

    # 检查是否为纯符号或数字
    text_content = re.sub(r'[^\w\u4e00-\u9fff]', '', line)
    if len(text_content) < 2:
        return True

    return False


def clean_line_lightly(line):
    """
    对保留的行进行轻度清洗

    Args:
        line: 输入行

    Returns:
        清洗后的行
    """
    # 去除行内的JavaScript代码
    line = re.sub(r'alert\s*\([^)]*\)', '', line)
    line = re.sub(r'function\s+\w+\s*\([^)]*\)\s*\{[^}]*\}', '', line)

    # 去除行内的时间戳（但保留文本内容）
    line = re.sub(r'发布时间：\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', '', line)
    line = re.sub(r'更新时间：\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', '', line)

    # 去除行内的编辑信息（但保留文本内容）
    line = re.sub(r'编辑：[^\s]*\s*', '', line)
    line = re.sub(r'责任编辑：[^\s]*\s*', '', line)
    line = re.sub(r'作者：[^\s]*\s*', '', line)

    # 清理多余空格
    line = re.sub(r'\s+', ' ', line)
    line = line.strip()

    return line
